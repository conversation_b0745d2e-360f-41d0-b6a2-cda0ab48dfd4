"""
Centralized role and permission management for Cortexa services.

This module defines the role hierarchy and permission system used across
all microservices in the Cortexa platform.
"""

from enum import Enum
from typing import Set


class UserRole(str, Enum):
    """User roles in the system."""
    
    ADMIN = "ADMIN"
    MANAGER = "MANAGER"
    OPERATOR = "OPERATOR"
    TRANSLATOR = "TRANSLATOR"


class Permission(str, Enum):
    """System permissions."""
    
    # User management
    CREATE_USER = "create_user"
    READ_USER = "read_user"
    UPDATE_USER = "update_user"
    DELETE_USER = "delete_user"
    LIST_USERS = "list_users"
    
    # Self management
    READ_SELF = "read_self"
    UPDATE_SELF = "update_self"
    UPDATE_SELF_PASSWORD = "update_self_password"
    
    # Call data
    READ_CALL_DATA = "read_call_data"
    READ_CALL_STATISTICS = "read_call_statistics"
    
    # Translation and voice services
    TRANSLATE_CONTENT = "translate_content"
    USE_VOICE_TRANSLATION = "use_voice_translation"


# Role-based permissions mapping
ROLE_PERMISSIONS: dict[UserRole, Set[Permission]] = {
    UserRole.ADMIN: {
        # Full user management
        Permission.CREATE_USER,
        Permission.READ_USER,
        Permission.UPDATE_USER,
        Permission.DELETE_USER,
        Permission.LIST_USERS,
        
        # Self management
        Permission.READ_SELF,
        Permission.UPDATE_SELF,
        Permission.UPDATE_SELF_PASSWORD,
        
        # Call data access
        Permission.READ_CALL_DATA,
        Permission.READ_CALL_STATISTICS,
        
        # Translation and voice services
        Permission.TRANSLATE_CONTENT,
        Permission.USE_VOICE_TRANSLATION,
    },
    
    UserRole.MANAGER: {
        # Limited user management
        Permission.READ_USER,
        Permission.LIST_USERS,
        
        # Self management
        Permission.READ_SELF,
        Permission.UPDATE_SELF,
        Permission.UPDATE_SELF_PASSWORD,
        
        # Call data access
        Permission.READ_CALL_DATA,
        Permission.READ_CALL_STATISTICS,
        
        # Voice services
        Permission.USE_VOICE_TRANSLATION,
    },
    
    UserRole.OPERATOR: {
        # Self management only
        Permission.READ_SELF,
        Permission.UPDATE_SELF,
        Permission.UPDATE_SELF_PASSWORD,
        
        # Basic call data access
        Permission.READ_CALL_DATA,
        
        # Voice services
        Permission.USE_VOICE_TRANSLATION,
    },
    
    UserRole.TRANSLATOR: {
        # Self management
        Permission.READ_SELF,
        Permission.UPDATE_SELF,
        Permission.UPDATE_SELF_PASSWORD,
        
        # Translation specific
        Permission.TRANSLATE_CONTENT,
        Permission.USE_VOICE_TRANSLATION,
        
        # Basic call data access (for translation context)
        Permission.READ_CALL_DATA,
    },
}


def get_valid_roles() -> Set[str]:
    """Get all valid role names."""
    return {role.value for role in UserRole}


def get_role_permissions(role: str) -> Set[Permission]:
    """Get permissions for a specific role."""
    try:
        user_role = UserRole(role)
        return ROLE_PERMISSIONS.get(user_role, set())
    except ValueError:
        return set()


def has_permission(role: str, permission: Permission) -> bool:
    """Check if a role has a specific permission."""
    role_permissions = get_role_permissions(role)
    return permission in role_permissions


def get_roles_with_permission(permission: Permission) -> Set[str]:
    """Get all roles that have a specific permission."""
    roles = set()
    for role, permissions in ROLE_PERMISSIONS.items():
        if permission in permissions:
            roles.add(role.value)
    return roles


# Convenience functions for common permission checks
def can_manage_users(role: str) -> bool:
    """Check if role can manage users."""
    return has_permission(role, Permission.CREATE_USER)


def can_use_voice_translation(role: str) -> bool:
    """Check if role can use voice translation services."""
    return has_permission(role, Permission.USE_VOICE_TRANSLATION)


def can_read_call_data(role: str) -> bool:
    """Check if role can read call data."""
    return has_permission(role, Permission.READ_CALL_DATA)
