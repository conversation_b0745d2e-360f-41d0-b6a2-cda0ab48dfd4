"""
Centralized authentication middleware for Cortexa services.

This module provides reusable authentication logic that can be used
across all microservices, including WebSocket authentication.
"""
from fastapi import Depends, HTTPException, status

from .auth import TokenData, verify_token
from .roles import Permission, has_permission
from ..config import SecuritySettings


async def get_current_user_ws(
    token: str | None = None,
    security_settings: SecuritySettings | None = None,
) -> TokenData:
    """
    Get current user from WebSocket JWT token.

    Since WebSocket connections from browsers cannot use Authorization headers,
    the JWT token must be passed as a query parameter.

    Args:
        token: JWT access token from query parameter
        security_settings: Security settings (optional, will create if not provided)

    Returns:
        TokenData: Decoded token data with user information

    Raises:
        HTTPException: If token is missing or invalid
    """
    if token is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Access token is required for WebSocket connection",
        )

    try:
        # Use provided settings or create new ones
        if security_settings is None:
            security_settings = SecuritySettings()
        
        token_data = verify_token(token, security_settings, token_type="access")
        return token_data
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired access token",
        )


class PermissionChecker:
    """Dependency to check if user has required permissions."""
    
    def __init__(self, required_permissions: list[Permission]):
        """Initialize with required permissions."""
        self.required_permissions = required_permissions
    
    def __call__(self, current_user: TokenData = Depends(get_current_user_ws)) -> TokenData:
        """Check if user has required permissions."""
        if not current_user.role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User role not found"
            )
        
        # Check if user has all required permissions
        for permission in self.required_permissions:
            if not has_permission(current_user.role, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{permission.value}' required"
                )
        
        return current_user


def require_permissions(*permissions: Permission):
    """
    Decorator factory to create permission checkers.
    
    Usage:
        @require_permissions(Permission.USE_VOICE_TRANSLATION)
        async def voice_endpoint(user: TokenData = Depends(...)):
            pass
    """
    return PermissionChecker(list(permissions))


async def verify_voice_translation_access(
    user: TokenData,
) -> bool:
    """
    Verify user has access to voice translation services.
    
    Args:
        user: Token data with user information
        
    Returns:
        bool: True if user has access
        
    Raises:
        HTTPException: If user doesn't have required permissions
    """
    if not user.role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User role not found"
        )
    
    if not has_permission(user.role, Permission.USE_VOICE_TRANSLATION):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Voice translation access not permitted for your role"
        )
    
    return True


# Convenience dependency for voice translation access
voice_translation_required = require_permissions(Permission.USE_VOICE_TRANSLATION)
