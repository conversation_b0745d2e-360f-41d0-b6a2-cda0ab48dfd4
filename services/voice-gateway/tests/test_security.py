import pytest
from unittest.mock import patch, MagicMock
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from uuid import uuid4

from cortexacommon.security.auth import TokenD<PERSON>, create_access_token
from cortexacommon.security import get_current_user_ws, verify_voice_translation_access
from cortexacommon.config import SecuritySettings


class TestGetCurrentUserWS:
    """Test suite for WebSocket JWT authentication."""

    @pytest.mark.asyncio
    async def test_get_current_user_ws_success(self, mock_user):
        """Test successful WebSocket authentication with valid token."""
        test_token = "valid.jwt.token"

        with patch("cortexacommon.security.middleware.verify_token") as mock_verify:
            mock_verify.return_value = mock_user

            result = await get_current_user_ws(token=test_token)

            assert result == mock_user
            mock_verify.assert_called_once()
            # Verify the token verification was called with correct parameters
            args, kwargs = mock_verify.call_args
            assert args[0] == test_token
            assert isinstance(args[1], SecuritySettings)
            assert kwargs.get("token_type") == "access"

    @pytest.mark.asyncio
    async def test_get_current_user_ws_missing_token(self):
        """Test WebSocket authentication fails when token is missing."""
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_ws(token=None)
        
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Access token is required" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_current_user_ws_invalid_token(self):
        """Test WebSocket authentication fails with invalid token."""
        test_token = "invalid.jwt.token"

        with patch("cortexacommon.security.middleware.verify_token") as mock_verify:
            mock_verify.side_effect = Exception("Invalid token")

            with pytest.raises(HTTPException) as exc_info:
                await get_current_user_ws(token=test_token)

            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid or expired access token" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_current_user_ws_expired_token(self):
        """Test WebSocket authentication fails with expired token."""
        test_token = "expired.jwt.token"
        
        with patch("src.core.security.verify_token") as mock_verify:
            mock_verify.side_effect = Exception("Token expired")
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user_ws(token=test_token)
            
            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid or expired access token" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_current_user_ws_empty_token(self):
        """Test WebSocket authentication fails with empty token."""
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_ws(token="")
        
        # Empty string should be treated as missing token
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED


class TestVerifyVoiceTranslationAccess:
    """Test suite for voice translation access verification."""

    @pytest.mark.asyncio
    async def test_verify_voice_translation_access_admin(self, mock_user):
        """Test voice translation access for admin role."""
        mock_user.role = "ADMIN"
        result = await verify_voice_translation_access(mock_user)
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_voice_translation_access_manager(self, mock_user):
        """Test voice translation access for manager role."""
        mock_user.role = "MANAGER"
        result = await verify_voice_translation_access(mock_user)
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_voice_translation_access_operator(self, mock_user):
        """Test voice translation access for operator role."""
        mock_user.role = "OPERATOR"
        result = await verify_voice_translation_access(mock_user)
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_voice_translation_access_translator(self, mock_user):
        """Test voice translation access for translator role."""
        mock_user.role = "TRANSLATOR"
        result = await verify_voice_translation_access(mock_user)
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_voice_translation_access_no_role(self, mock_user):
        """Test voice translation access when user has no role."""
        mock_user.role = None
        with pytest.raises(HTTPException) as exc_info:
            await verify_voice_translation_access(mock_user)

        assert exc_info.value.status_code == status.HTTP_403_FORBIDDEN
        assert "User role not found" in exc_info.value.detail


class TestJWTIntegration:
    """Test suite for JWT token integration between services."""

    @pytest.mark.asyncio
    async def test_jwt_token_verification_with_same_secret(self):
        """Test that JWT tokens can be created and verified with the same secret key."""
        # Create a security settings instance (this will use the .env file)
        security_settings = SecuritySettings()

        # Create test user data
        user_id = str(uuid4())
        test_data = {
            "sub": user_id,
            "role": "OPERATOR"
        }

        # Create a JWT token (simulating what authenticator service does)
        token = create_access_token(data=test_data, security_settings=security_settings)

        # Verify the token can be decoded (simulating what voice-gateway does)
        result = await get_current_user_ws(token=token)

        # Verify the decoded data matches
        assert str(result.user_id) == user_id
        assert result.role == "OPERATOR"

    @pytest.mark.asyncio
    async def test_jwt_token_verification_with_different_secret_fails(self):
        """Test that JWT tokens fail verification when using different secret keys."""
        # Create token with one secret key
        security_settings_1 = SecuritySettings()
        security_settings_1.secret_key = "secret-key-1"

        user_id = str(uuid4())
        test_data = {
            "sub": user_id,
            "role": "OPERATOR"
        }

        token = create_access_token(data=test_data, security_settings=security_settings_1)

        # Try to verify with different secret key
        security_settings_2 = SecuritySettings()
        security_settings_2.secret_key = "secret-key-2"  # Different secret key

        # This should fail with invalid signature
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_ws(token=token, security_settings=security_settings_2)

        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Invalid or expired access token" in exc_info.value.detail
