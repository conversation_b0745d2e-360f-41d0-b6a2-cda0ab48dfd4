"""
OpenTelemetry tracing configuration for voice-gateway.

This module provides a simplified interface to the cortexacommon tracing functionality.
"""

from typing import Optional

from opentelemetry import trace
from cortexacommon.monitoring import setup_tracing as common_setup_tracing, get_tracer as common_get_tracer
from cortexacommon.monitoring.config import get_monitoring_settings
from cortexacommon.logging import get_logger

from .config import settings

logger = get_logger(__name__)

# Global tracer instance
_tracer: Optional[trace.Tracer] = None


def init_tracing():
    """Initialize tracing via cortexacommon monitoring."""
    global _tracer
    try:
        monitoring_settings = get_monitoring_settings(settings.service_name)
        _tracer = common_setup_tracing(monitoring_settings.tracing)
        if _tracer:
            logger.info("Tracing initialized via cortexacommon")
        else:
            logger.info("Tracing disabled")
    except Exception as e:
        logger.error(f"Failed to initialize tracing: {e}")
        _tracer = None


def get_tracer(name: Optional[str] = None) -> Optional[trace.Tracer]:
    """Get a tracer instance."""
    return common_get_tracer(name or __name__)


def get_global_tracer() -> Optional[trace.Tracer]:
    """Get the global tracer instance."""
    return _tracer
