"""
Role and permission definitions for the authenticator service.

This module now imports from the centralized cortexacommon.security.roles
to maintain consistency across all services.
"""

# Import from centralized roles module
from cortexacommon.security.roles import (
    UserRole,
    Permission,
    get_valid_roles,
    get_role_permissions,
    has_permission,
    get_roles_with_permission,
    can_manage_users,
)

# Re-export for backward compatibility
__all__ = [
    "UserRole",
    "Permission",
    "get_valid_roles",
    "get_role_permissions",
    "has_permission",
    "get_roles_with_permission",
    "can_manage_users",
]


def can_view_users(role: str) -> bool:
    """Check if role can view user lists."""
    return has_permission(role, Permission.LIST_USERS)


def can_view_statistics(role: str) -> bool:
    """Check if role can view call statistics."""
    return has_permission(role, Permission.READ_CALL_STATISTICS)
